/* Network Background Styles */
.network-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 0;
  pointer-events: none;
}

.network-background canvas {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
}

/* Ensure content is above the background */
.content-overlay {
  position: relative;
  z-index: 10;
}

/* Add subtle backdrop for better text readability */
.content-backdrop {
  backdrop-filter: blur(0.5px);
  background: rgba(255, 255, 255, 0.02);
}

.dark .content-backdrop {
  background: rgba(0, 0, 0, 0.02);
}

/* Animation for smooth theme transitions */
.network-background,
.content-backdrop {
  transition: all 0.3s ease-in-out;
}
