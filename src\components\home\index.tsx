'use client';

import React from 'react';
import { useTranslations } from 'next-intl';
import { Button } from "@/components/ui/button";
import NetworkBackground from '../NetworkBackground';

export default function HomeComponent() {
  const t = useTranslations("home");



  return (
    <div className="min-h-screen flex flex-col bg-white dark:bg-black text-black dark:text-white transition-all duration-300 relative overflow-hidden">
      <NetworkBackground className="z-0" />
      <div className="flex-1 flex flex-col items-center justify-center relative z-10 content-overlay">
        <div className="w-full max-w-6xl px-4">
          <div className="max-w-4xl text-center mt-18 space-y-10 px-4 mx-auto content-backdrop rounded-3xl py-12">
            <h1 className="text-6xl font-bold bg-gradient-to-r from-[#30C59B] to-[#00A3FF] bg-clip-text text-transparent">
              {t('title')}
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
              { t('subtitle')}
            </p>
           

            <div className="flex gap-4 justify-center flex-wrap">
              <Button size="lg" className="px-8 py-3  ">
                {t('cta')}
              </Button>
              <Button variant="outline" size="lg" className="px-8 py-3">
                {t('learnMore')}
              </Button>
            </div>

            <div className="mt-16">
              <h2 className="text-3xl font-semibold mb-8">{t('features.title')}</h2>
              <div className="grid md:grid-cols-3 gap-8">
                <div className="p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                  <h3 className="text-xl font-semibold mb-3">{t('features.feature1.title')}</h3>
                  <p className="text-gray-600 dark:text-gray-300">{t('features.feature1.description')}</p>
                </div>
                <div className="p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                  <h3 className="text-xl font-semibold mb-3">{t('features.feature2.title')}</h3>
                  <p className="text-gray-600 dark:text-gray-300">{t('features.feature2.description')}</p>
                </div>
                <div className="p-6 rounded-lg border border-gray-200 dark:border-gray-700">
                  <h3 className="text-xl font-semibold mb-3">{t('features.feature3.title')}</h3>
                  <p className="text-gray-600 dark:text-gray-300">{t('features.feature3.description')}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
