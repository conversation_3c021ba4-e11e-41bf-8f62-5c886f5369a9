import React from 'react'
import ClientWrapper from '../ClientWrapper';
import NetworkBackground from '../NetworkBackground';
import { getTranslations } from 'next-intl/server';
import { Button } from "@/components/ui/button";
export default async function  HomeComponent() {
 
  const t = await getTranslations("home");



  return (
    <ClientWrapper>
      <div className="min-h-screen flex flex-col bg-white dark:bg-black text-black dark:text-white transition-all duration-300 relative overflow-hidden">
        <NetworkBackground className="z-0" />
        <div className="flex-1 flex flex-col items-center justify-center relative z-10 content-overlay">
          <div className="w-full max-w-6xl px-4">


            <div className="max-w-4xl text-center mt-18 space-y-10 px-4 mx-auto content-backdrop rounded-3xl py-12">
              <h1 className="text-6xl font-bold bg-gradient-to-r from-[#30C59B] to-[#00A3FF] bg-clip-text text-transparent">
                {t('title')}
              </h1>
              <p className="text-xl text-gray-600 dark:text-gray-300 max-w-2xl mx-auto">
                {t('subtitle')}
              </p>

              <div className="flex gap-4 justify-center flex-wrap">
                <Button size="lg" className="px-8 py-3">
                  {t('cta')}
                </Button>
                <Button variant="outline" size="lg" className="px-8 py-3">
                  {t('learnMore')}
                </Button>
              </div>


            </div>
          </div>
        </div>
      </div>
    </ClientWrapper>
  );
}
